{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5146", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "iisSettings": {"windowsAuthentication": true, "anonymousAuthentication": false}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:7085;http://localhost:5146", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "iisSettings": {"windowsAuthentication": true, "anonymousAuthentication": false}}}}