using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class SyndicationService(ApplicationDbContext context)
{
    public Task<List<SyndicationDto>> GetSyndicationsAsync()
    {
        var syndication = (from a in context.Syndications
            orderby a.Program.ProgramName
            select new SyndicationDto()
            {
                ProgramName = a.Program.ProgramName,
                ProgramId = a.ProgramId,
                ClientName = a.Client.ClientName,
                ClientId = a.ClientId,
                ContractEndDate = a.ContractEndDate,
                ContractStartDate = a.ContractStartDate,
                CurrencyCode = a.Currency.Code,
                CurrencyId = a.CurrencyId,
                EpisodeFrom = a.EpisodeFrom,
                EpisodeTo = a.EpisodeTo,
                Id = a.Id,
                LanguageId = a.LanguageId,
                OnAirFormatId = a.OnAirFormatId,
                LanguageName = a.Language.Title,
                OnAirFormatName = a.OnAirFormat.FormatName,
                PricePerEpisode = a.PricePerEpisode,
                Remarks = a.Remark,
                SyndicationFormatId = a.SyndicationFormatId,
                SyndicationFormatName = a.SyndicationFormat.FormatName,
                TerritoryId = a.TerritoryId,
                TerritoryName = a.Territory.Title,
                EpisodeByRange = a.EpisodeByRange,
                TotalEpisodes = a.TotalEpisodes
            }).ToList();
        return Task.FromResult(syndication);
    }

    public Task<List<SyndicationDto>> GetSyndicationsByProgramIdAsync(int programId)
    {
        var res = context.Syndications
            .Include(s => s.Program)
            .Include(s => s.Client)
            .Include(s => s.SyndicationFormat)
            .Include(s => s.Language)
            .Include(s => s.Territory)
            .Include(s => s.Currency)
            .Where(s => s.ProgramId == programId)
            .Select(s => new SyndicationDto
            {
                Id = s.Id,
                ProgramId = s.ProgramId,
                ProgramName = s.Program.ProgramName,
                ClientId = s.ClientId,
                ClientName = s.Client.ClientName,
                SyndicationFormatId = s.SyndicationFormatId,
                SyndicationFormatName = s.SyndicationFormat.FormatName,
                LanguageId = s.LanguageId,
                LanguageName = s.Language.Title,
                Remarks = s.Remark,
                CreatedDate = s.CreatedDate,
                ModifiedDate = s.ModifiedDate,
                // New fields
                ContractStartDate = s.ContractStartDate,
                ContractEndDate = s.ContractEndDate,
                TerritoryId = s.TerritoryId,
                TerritoryName = s.Territory.Title,
                EpisodeFrom = s.EpisodeFrom,
                EpisodeTo = s.EpisodeTo,
                PricePerEpisode = s.PricePerEpisode,
                CurrencyId = s.CurrencyId,
                CurrencyCode = s.Currency.Code,
                EpisodeByRange = s.EpisodeByRange,
                TotalEpisodes = s.TotalEpisodes
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<SyndicationDto?> GetSyndicationByIdAsync(int id)
    {
        var syndication = (from a in context.Syndications
            where a.Id == id
            select new SyndicationDto()
            {
                ProgramName = a.Program.ProgramName, ProgramId = a.ProgramId, ClientName = a.Client.ClientName,
                ClientId = a.ClientId, ContractEndDate = a.ContractEndDate, ContractStartDate = a.ContractStartDate,
                CurrencyCode = a.Currency.Code, CurrencyId = a.CurrencyId, EpisodeFrom = a.EpisodeFrom,
                EpisodeTo = a.EpisodeTo, Id = a.Id, LanguageId = a.LanguageId, 
                OnAirFormatId = a.OnAirFormatId,
                LanguageName = a.Language.Title, 
                OnAirFormatName = a.OnAirFormat.FormatName,
                PricePerEpisode = a.PricePerEpisode,
                Remarks = a.Remark, SyndicationFormatId = a.SyndicationFormatId, SyndicationFormatName = a.SyndicationFormat.FormatName,
                TerritoryId = a.TerritoryId, TerritoryName = a.Territory.Title,
                EpisodeByRange = a.EpisodeByRange,
                TotalEpisodes = a.TotalEpisodes
            }).FirstOrDefault();
        return Task.FromResult(syndication);
        //var syndication = context.Syndications
        //    .Include(s => s.Program)
        //    .Include(s => s.Client)
        //    .Include(s => s.SyndicationFormat)
        //    .Include(s => s.Language)
        //    .Include(s => s.Territory)
        //    .Include(s => s.Currency)
        //    .FirstOrDefault(s => s.Id == id);

        //if (syndication == null)
        //    return Task.FromResult<SyndicationDto?>(null);

        //var res = new SyndicationDto
        //{
        //    Id = syndication.Id,
        //    ProgramId = syndication.ProgramId,
        //    ProgramName = syndication.Program?.ProgramName,
        //    ClientId = syndication.ClientId,
        //    ClientName = syndication.Client?.ClientName,
        //    SyndicationFormatId = syndication.SyndicationFormatId,
        //    SyndicationFormatName = syndication.SyndicationFormat?.FormatName,
        //    LanguageId = syndication.LanguageId,
        //    LanguageName = syndication.Language?.Title,
        //    Remarks = syndication.Remark,
        //    CreatedDate = syndication.CreatedDate,
        //    ModifiedDate = syndication.ModifiedDate,
        //    // New fields
        //    ContractStartDate = syndication.ContractStartDate,
        //    ContractEndDate = syndication.ContractEndDate,
        //    TerritoryId = syndication.TerritoryId,
        //    TerritoryName = syndication.Territory?.Title,
        //    EpisodeFrom = syndication.EpisodeFrom,
        //    EpisodeTo = syndication.EpisodeTo,
        //    PricePerEpisode = syndication.PricePerEpisode,
        //    CurrencyId = syndication.CurrencyId,
        //    CurrencyCode = syndication.Currency?.Code
        //};
        //return Task.FromResult<SyndicationDto?>(res);
    }

    public Task<Syndication> CreateSyndicationAsync(SyndicationDto syndicationDto, string userId)
    {
        var syndication = new Syndication
        {
           
            ProgramId = syndicationDto.ProgramId,
            ClientId = syndicationDto.ClientId,
            SyndicationFormatId = syndicationDto.SyndicationFormatId,
            LanguageId = syndicationDto.LanguageId,
            Remark = syndicationDto.Remarks,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            // New fields
            ContractStartDate = syndicationDto.ContractStartDate,
            ContractEndDate = syndicationDto.ContractEndDate,
            TerritoryId = syndicationDto.TerritoryId,
            EpisodeFrom = syndicationDto.EpisodeFrom,
            EpisodeTo = syndicationDto.EpisodeTo,
            PricePerEpisode = syndicationDto.PricePerEpisode,
            CurrencyId = syndicationDto.CurrencyId,
            OnAirFormatId = syndicationDto.OnAirFormatId,
            CreatedBy = userId,
            EpisodeByRange = syndicationDto.EpisodeByRange,
            TotalEpisodes = syndicationDto.TotalEpisodes
        };

        context.Syndications.Add(syndication);
        context.SaveChanges();

        if (syndicationDto.EpisodeByRange)
        {
            syndication.EpisodeFrom = syndicationDto.EpisodeFrom;
            syndication.EpisodeTo = syndicationDto.EpisodeTo;
            syndication.TotalEpisodes = (syndicationDto.EpisodeTo ?? 0) - (syndicationDto.EpisodeFrom ?? 0) + 1;
        }
        else
        {
            syndication.EpisodeFrom = null;
            syndication.EpisodeTo = null;
            syndication.TotalEpisodes = syndicationDto.TotalEpisodes;
        }
        context.SaveChanges();
        return Task.FromResult(syndication);
    }

    public Task UpdateSyndicationAsync(SyndicationDto syndicationDto, string userId)
    {
        var syndication = context.Syndications.Find(syndicationDto.Id);
        if (syndication != null)
        {
            syndication.ProgramId = syndicationDto.ProgramId;
            syndication.ClientId = syndicationDto.ClientId;
            syndication.SyndicationFormatId = syndicationDto.SyndicationFormatId;
            syndication.LanguageId = syndicationDto.LanguageId;
            syndication.Remark = syndicationDto.Remarks;
            syndication.ModifiedDate = DateTime.Now;
            syndication.ModifiedBy = userId; // Assuming you have a ModifiedBy field in your Syndication model

            // Update new fields
            syndication.ContractStartDate = syndicationDto.ContractStartDate;
            syndication.ContractEndDate = syndicationDto.ContractEndDate;
            syndication.TerritoryId = syndicationDto.TerritoryId;
            if (syndicationDto.EpisodeByRange)
            {
                syndication.EpisodeFrom = syndicationDto.EpisodeFrom;
                syndication.EpisodeTo = syndicationDto.EpisodeTo;
                syndication.TotalEpisodes = (syndicationDto.EpisodeTo ?? 0) - (syndicationDto.EpisodeFrom ?? 0) + 1;
            }
            else
            {
                syndication.EpisodeFrom = null;
                syndication.EpisodeTo = null;
                syndication.TotalEpisodes = syndicationDto.TotalEpisodes;
            }

            syndication.PricePerEpisode = syndicationDto.PricePerEpisode;
            syndication.CurrencyId = syndicationDto.CurrencyId;
            syndication.OnAirFormatId = syndicationDto.OnAirFormatId;
            syndication.EpisodeByRange = syndicationDto.EpisodeByRange;
            
            context.SaveChanges();
        }
        return Task.CompletedTask;
    }

    public Task DeleteSyndicationAsync(int id)
    {
        var syndication = context.Syndications.Find(id);
        if (syndication != null)
        {
            context.Syndications.Remove(syndication);
            context.SaveChanges();
        }
        return Task.CompletedTask;
    }

    // Soft delete method
    public Task SoftDeleteSyndicationAsync(int id)
    {
        var syndication = context.Syndications.Find(id);
        if (syndication != null)
        {
            // Note: The current Syndication entity doesn't have IsActive field
            // This would need to be added to the database schema
            // For now, we'll use hard delete
            context.Syndications.Remove(syndication);
            context.SaveChanges();
        }
        return Task.CompletedTask;
    }
}
