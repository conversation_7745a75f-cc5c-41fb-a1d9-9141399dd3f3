using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class UserService(ApplicationDbContext context)
{
    public Task<List<UserDto>> GetUsersAsync()
    {
        var users = context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .Select(u => new UserDto
            {
                UserId = u.UserId,
                FullName = u.FullName,
                EmployeeCode = u.EmployeeCode,
                IsActive = u.IsActive,
                CreatedDate = u.CreatedDate,
                ModifiedDate = u.ModifiedDate,
                CreatedBy = u.CreatedBy,
                ModifiedBy = u.ModifiedBy,
                AssignedRoleIds = u.UserRoles.Select(ur => ur.RoleId).ToList(),
                AssignedRoles = u.UserRoles.Select(ur => new RoleDto
                {
                    RoleId = ur.Role.RoleId,
                    RoleTitle = ur.Role.RoleTitle,
                    RoleIsActive = ur.Role.RoleIsActive
                }).ToList()
            })
            .OrderBy(u => u.FullName)
            .ToList();

        return Task.FromResult(users);
    }

    public Task<UserDto?> GetUserByIdAsync(string userId)
    {
        var user = context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .Where(u => u.UserId == userId)
            .Select(u => new UserDto
            {
                UserId = u.UserId,
                FullName = u.FullName,
                EmployeeCode = u.EmployeeCode,
                IsActive = u.IsActive,
                CreatedDate = u.CreatedDate,
                ModifiedDate = u.ModifiedDate,
                CreatedBy = u.CreatedBy,
                ModifiedBy = u.ModifiedBy,
                AssignedRoleIds = u.UserRoles.Select(ur => ur.RoleId).ToList(),
                AssignedRoles = u.UserRoles.Select(ur => new RoleDto
                {
                    RoleId = ur.Role.RoleId,
                    RoleTitle = ur.Role.RoleTitle,
                    RoleIsActive = ur.Role.RoleIsActive
                }).ToList()
            })
            .FirstOrDefault();

        return Task.FromResult(user);
    }

    public Task<UserDto> CreateUserAsync(UserDto userDto)
    {
        var user = new User
        {
            UserId = userDto.UserId!,
            FullName = userDto.FullName,
            EmployeeCode = userDto.EmployeeCode,
            IsActive = userDto.IsActive,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = userDto.CreatedBy,
            ModifiedBy = userDto.ModifiedBy
        };

        context.Users.Add(user);
        context.SaveChanges();

        // Assign roles to user
        if (userDto.AssignedRoleIds.Any())
        {
            var userRoles = userDto.AssignedRoleIds.Select(roleId => new UserRole
            {
                UserId = user.UserId,
                RoleId = roleId,
                CreatedDate = DateTime.Now,
                CreatedBy = userDto.CreatedBy
            }).ToList();

            context.UserRoles.AddRange(userRoles);
            context.SaveChanges();
        }

        return Task.FromResult(userDto);
    }

    public Task<bool> UpdateUserAsync(UserDto userDto)
    {
        var user = context.Users.Find(userDto.UserId);
        if (user == null)
        {
            return Task.FromResult(false);
        }

        user.FullName = userDto.FullName;
        user.EmployeeCode = userDto.EmployeeCode;
        user.IsActive = userDto.IsActive;
        user.ModifiedDate = DateTime.Now;
        user.ModifiedBy = userDto.ModifiedBy;

        // Update user roles
        var existingUserRoles = context.UserRoles.Where(ur => ur.UserId == userDto.UserId).ToList();
        context.UserRoles.RemoveRange(existingUserRoles);

        if (userDto.AssignedRoleIds.Any())
        {
            var newUserRoles = userDto.AssignedRoleIds.Select(roleId => new UserRole
            {
                UserId = userDto.UserId!,
                RoleId = roleId,
                CreatedDate = DateTime.Now,
                CreatedBy = userDto.ModifiedBy
            }).ToList();

            context.UserRoles.AddRange(newUserRoles);
        }

        context.SaveChanges();
        return Task.FromResult(true);
    }

    public Task<bool> DeleteUserAsync(string userId)
    {
        var user = context.Users.Find(userId);
        if (user == null)
        {
            return Task.FromResult(false);
        }

        // Remove user roles first
        var userRoles = context.UserRoles.Where(ur => ur.UserId == userId).ToList();
        context.UserRoles.RemoveRange(userRoles);

        context.Users.Remove(user);
        context.SaveChanges();
        return Task.FromResult(true);
    }

    public Task<List<UserRoleDto>> GetUserRolesAsync(string userId)
    {
        var userRoles = context.UserRoles
            .Include(ur => ur.User)
            .Include(ur => ur.Role)
            .Where(ur => ur.UserId == userId)
            .Select(ur => new UserRoleDto
            {
                UserId = ur.UserId,
                RoleId = ur.RoleId,
                CreatedDate = ur.CreatedDate,
                CreatedBy = ur.CreatedBy,
                UserFullName = ur.User.FullName,
                RoleTitle = ur.Role.RoleTitle
            })
            .ToList();

        return Task.FromResult(userRoles);
    }

    public Task<bool> IsUserAdminAsync(string userId)
    {
        // Check if user is the specific admin user or has admin role
        if (userId == "khi-soft-056\\jawaid")
        {
            return Task.FromResult(true);
        }

        var hasAdminRole = context.UserRoles
            .Include(ur => ur.Role)
            .Any(ur => ur.UserId == userId && 
                      ur.Role.RoleTitle!.ToLower().Contains("admin") && 
                      ur.Role.RoleIsActive);

        return Task.FromResult(hasAdminRole);
    }

    public Task<List<MenuDto>> GetUserMenusAsync(string userId)
    {
        var userMenus = context.UserRoles
            .Include(ur => ur.Role)
            .ThenInclude(r => r.RoleMenus)
            .ThenInclude(rm => rm.Menu)
            .Where(ur => ur.UserId == userId && ur.Role.RoleIsActive && ur.User.IsActive && ur.Role.RoleIsActive)
            .SelectMany(ur => ur.Role.RoleMenus)
            .Select(rm => rm.Menu)
            .Distinct()
            .Select(m => new MenuDto
            {
                MenuId = m.MenuId,
                MemuTitle = m.MemuTitle,
                MenuLink = m.MenuLink,
                SortOrder = m.SortOrder,
                Module = m.Module,
                Icon = m.Icon
            })
            .OrderBy(m => m.Module)
            .ThenBy(m => m.SortOrder)
            .ThenBy(m => m.MemuTitle)
            .ToList();

        return Task.FromResult(userMenus);
    }
}
