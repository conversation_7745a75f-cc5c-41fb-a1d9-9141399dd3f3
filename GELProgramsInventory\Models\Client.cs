﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace GELProgramsInventory.Models;

public partial class Client
{
    public int ClientId { get; set; }

    public string ClientName { get; set; }

    public string ContactInfo { get; set; }

    public bool? IsActive { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string CreatedBy { get; set; }

    public string ModifiedBy { get; set; }

    public virtual ICollection<Program> Programs { get; set; } = new List<Program>();

    public virtual ICollection<Syndication> Syndications { get; set; } = new List<Syndication>();
}