﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Models;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Client> Clients { get; set; }

    public virtual DbSet<ContractDocument> ContractDocuments { get; set; }

    public virtual DbSet<Currency> Currencies { get; set; }

    public virtual DbSet<Genre> Genres { get; set; }

    public virtual DbSet<Language> Languages { get; set; }

    public virtual DbSet<Menu> Menus { get; set; }

    public virtual DbSet<OnAirFormat> OnAirFormats { get; set; }

    public virtual DbSet<ProductionHouse> ProductionHouses { get; set; }

    public virtual DbSet<Program> Programs { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RoleMenu> RoleMenus { get; set; }

    public virtual DbSet<Syndication> Syndications { get; set; }

    public virtual DbSet<SyndicationFormat> SyndicationFormats { get; set; }

    public virtual DbSet<Territory> Territories { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserRole> UserRoles { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Client>(entity =>
        {
            entity.HasKey(e => e.ClientId).HasName("PK__Client__E67E1A0429572725");

            entity.ToTable("Client");

            entity.HasIndex(e => e.ClientName, "UQ__Client__65800DA02C3393D0").IsUnique();

            entity.Property(e => e.ClientId).HasColumnName("ClientID");
            entity.Property(e => e.ClientName)
                .IsRequired()
                .HasMaxLength(200);
            entity.Property(e => e.ContactInfo).HasMaxLength(500);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<ContractDocument>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Contract__3214EC0776969D2E");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.FileName)
                .IsRequired()
                .HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.UploadedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.UploadedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Syndication).WithMany(p => p.ContractDocuments)
                .HasForeignKey(d => d.SyndicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ContractD__Syndi__7A672E12");
        });

        modelBuilder.Entity<Currency>(entity =>
        {
            entity.HasKey(e => e.CurrencyId).HasName("PK__Currenci__14470AF0534D60F1");

            entity.HasIndex(e => e.Title, "UQ__Currenci__2CB664DC5FB337D6").IsUnique();

            entity.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.CreateDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Genre>(entity =>
        {
            entity.HasKey(e => e.GenreId).HasName("PK__Genre__0385055E20C1E124");

            entity.ToTable("Genre");

            entity.HasIndex(e => e.GenreName, "UQ__Genre__BBE1C339239E4DCF").IsUnique();

            entity.Property(e => e.GenreId).HasColumnName("GenreID");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.GenreName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<Language>(entity =>
        {
            entity.HasKey(e => e.LanguageId).HasName("PK__Language__B93855AB3C69FB99");

            entity.HasIndex(e => e.Title, "UQ__Language__2CB664DC3F466844").IsUnique();

            entity.Property(e => e.Title)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Menu>(entity =>
        {
            entity.HasKey(e => e.MenuId).HasName("PK__Menus__C99ED23010566F31");

            entity.HasIndex(e => e.MemuTitle, "UQ__Menus__89CC0E771332DBDC").IsUnique();

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Icon)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.MemuTitle)
                .IsRequired()
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.MenuLink)
                .IsRequired()
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Module)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.SortOrder).HasDefaultValue(1);
        });

        modelBuilder.Entity<OnAirFormat>(entity =>
        {
            entity.HasKey(e => e.OnAirFormatId).HasName("PK__OnAirFor__1BDA66A0182C9B23");

            entity.ToTable("OnAirFormat");

            entity.HasIndex(e => e.FormatName, "UQ__OnAirFor__CE1A17241B0907CE").IsUnique();

            entity.Property(e => e.OnAirFormatId).HasColumnName("OnAirFormatID");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FormatName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<ProductionHouse>(entity =>
        {
            entity.HasKey(e => e.ProductionHouseId).HasName("PK__Producti__132ACF740F975522");

            entity.ToTable("ProductionHouse");

            entity.HasIndex(e => e.ProductionHouseName, "UQ__Producti__A9D8F3941273C1CD").IsUnique();

            entity.Property(e => e.ProductionHouseId).HasColumnName("ProductionHouseID");
            entity.Property(e => e.ContactInfo).HasMaxLength(500);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.ProductionHouseName)
                .IsRequired()
                .HasMaxLength(200);
        });

        modelBuilder.Entity<Program>(entity =>
        {
            entity.HasKey(e => e.ProgramId).HasName("PK__Programs__7525603807F6335A");

            entity.HasIndex(e => e.DriveSerialNumber, "IX_Programs_DriveSerialNumber");

            entity.HasIndex(e => e.GenreId, "IX_Programs_GenreID");

            entity.HasIndex(e => e.IsSyndication, "IX_Programs_IsSyndication");

            entity.HasIndex(e => e.ProgramName, "IX_Programs_ProgramName");

            entity.HasIndex(e => e.YearOfLaunch, "IX_Programs_YearOfLaunch");

            entity.Property(e => e.ProgramId).HasColumnName("ProgramID");
            entity.Property(e => e.ClientId).HasColumnName("ClientID");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.DriveSerialNumber).HasMaxLength(500);
            entity.Property(e => e.FirstAiringEpisodeDate).HasColumnType("datetime");
            entity.Property(e => e.FreshEpisodes)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.GenreId).HasColumnName("GenreID");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsSyndication).HasDefaultValue(false);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.OnAirFormatId).HasColumnName("OnAirFormatID");
            entity.Property(e => e.ProductionHouseId).HasColumnName("ProductionHouseID");
            entity.Property(e => e.ProgramName)
                .IsRequired()
                .HasMaxLength(1000);
            entity.Property(e => e.SecondaryName1).HasMaxLength(1000);
            entity.Property(e => e.SecondaryName2).HasMaxLength(1000);
            entity.Property(e => e.SecondaryName3).HasMaxLength(1000);
            entity.Property(e => e.SecondaryName4).HasMaxLength(1000);
            entity.Property(e => e.SyndicationFormatId).HasColumnName("SyndicationFormatID");
            entity.Property(e => e.TotalEpisodes).HasDefaultValue(1);
            entity.Property(e => e.YearOfLaunch).HasMaxLength(4);

            entity.HasOne(d => d.Client).WithMany(p => p.Programs)
                .HasForeignKey(d => d.ClientId)
                .HasConstraintName("FK_Programs_Client");

            entity.HasOne(d => d.Genre).WithMany(p => p.Programs)
                .HasForeignKey(d => d.GenreId)
                .HasConstraintName("FK_Programs_Genre");

            entity.HasOne(d => d.OnAirFormat).WithMany(p => p.Programs)
                .HasForeignKey(d => d.OnAirFormatId)
                .HasConstraintName("FK_Programs_OnAirFormat");

            entity.HasOne(d => d.ProductionHouse).WithMany(p => p.Programs)
                .HasForeignKey(d => d.ProductionHouseId)
                .HasConstraintName("FK_Programs_ProductionHouse");

            entity.HasOne(d => d.SyndicationFormat).WithMany(p => p.Programs)
                .HasForeignKey(d => d.SyndicationFormatId)
                .HasConstraintName("FK_Programs_SyndicationFormat");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.RoleId).HasName("PK__Roles__8AFACE1A7E37BEF6");

            entity.HasIndex(e => e.RoleTitle, "UQ__Roles__D462F92901142BA1").IsUnique();

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.RoleIsActive).HasDefaultValue(true);
            entity.Property(e => e.RoleTitle)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<RoleMenu>(entity =>
        {
            entity.HasKey(e => new { e.RoleId, e.MenuId }).HasName("PK__RoleMenu__966323391AD3FDA4");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Menu).WithMany(p => p.RoleMenus)
                .HasForeignKey(d => d.MenuId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RoleMenus__MenuI__1EA48E88");

            entity.HasOne(d => d.Role).WithMany(p => p.RoleMenus)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__RoleMenus__RoleI__1DB06A4F");
        });

        modelBuilder.Entity<Syndication>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Syndications_Id");

            entity.Property(e => e.ContractEndDate).HasColumnType("datetime");
            entity.Property(e => e.ContractStartDate).HasColumnType("datetime");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.EpisodeByRange).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.PricePerEpisode).HasColumnType("money");
            entity.Property(e => e.Remark).IsUnicode(false);
            entity.Property(e => e.SyndicationFormatId).HasColumnName("SyndicationFormatID");

            entity.HasOne(d => d.Client).WithMany(p => p.Syndications)
                .HasForeignKey(d => d.ClientId)
                .HasConstraintName("FK__Syndicati__Clien__6A30C649");

            entity.HasOne(d => d.Currency).WithMany(p => p.Syndications)
                .HasForeignKey(d => d.CurrencyId)
                .HasConstraintName("FK__Syndicati__Curre__693CA210");

            entity.HasOne(d => d.Language).WithMany(p => p.Syndications)
                .HasForeignKey(d => d.LanguageId)
                .HasConstraintName("FK__Syndicati__Langu__68487DD7");

            entity.HasOne(d => d.OnAirFormat).WithMany(p => p.Syndications)
                .HasForeignKey(d => d.OnAirFormatId)
                .HasConstraintName("FK__Syndicati__OnAir__73BA3083");

            entity.HasOne(d => d.Program).WithMany(p => p.Syndications)
                .HasForeignKey(d => d.ProgramId)
                .HasConstraintName("FK__Syndicati__Progr__6754599E");

            entity.HasOne(d => d.SyndicationFormat).WithMany(p => p.Syndications)
                .HasForeignKey(d => d.SyndicationFormatId)
                .HasConstraintName("FK__Syndicati__Syndi__66603565");

            entity.HasOne(d => d.Territory).WithMany(p => p.Syndications)
                .HasForeignKey(d => d.TerritoryId)
                .HasConstraintName("FK__Syndicati__Terri__656C112C");
        });

        modelBuilder.Entity<SyndicationFormat>(entity =>
        {
            entity.HasKey(e => e.SyndicationFormatId).HasName("PK__Syndicat__A95ADF9A7F60ED59");

            entity.ToTable("SyndicationFormat");

            entity.HasIndex(e => e.FormatName, "UQ__Syndicat__CE1A1724023D5A04").IsUnique();

            entity.Property(e => e.SyndicationFormatId).HasColumnName("SyndicationFormatID");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FormatName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<Territory>(entity =>
        {
            entity.HasKey(e => e.TerritoryId).HasName("PK__Territor__2BECD2C44AB81AF0");

            entity.HasIndex(e => e.Title, "UQ__Territor__2CB664DC4D94879B").IsUnique();

            entity.Property(e => e.CreateDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Title)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("PK__Users__1788CC4C2180FB33");

            entity.Property(e => e.UserId)
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.EmployeeCode)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.FullName)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.RoleId }).HasName("PK__UserRole__AF2760AD282DF8C2");

            entity.Property(e => e.UserId)
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Role).WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserRoles__RoleI__2B0A656D");

            entity.HasOne(d => d.User).WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserRoles__UserI__2A164134");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}