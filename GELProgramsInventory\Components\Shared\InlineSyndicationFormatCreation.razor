@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Microsoft.AspNetCore.Authorization
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@inject SyndicationFormatService SyndicationFormatService
@attribute [Authorize]
@inject AuthenticationStateProvider AuthenticationStateProvider

<SfDialog @ref="FormatDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Add New Syndication Format</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="NewFormat" OnValidSubmit="SaveFormat" FormName="InlineFormatForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="form-group mb-3">
                        <label>Format Name</label>
                        <SfTextBox @bind-value="NewFormat.FormatName" Placeholder="Enter format name" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewFormat.FormatName)" />
                    </div>

                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card-body {
        padding: 1.5rem;
    }

    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>

@code {
    private SfDialog? FormatDialog;
    private SyndicationFormatDto NewFormat = new();

    [Parameter] public EventCallback<SyndicationFormatDto> OnFormatCreated { get; set; }


    private string _currentUserId = "";
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity is { IsAuthenticated: true })
        {
            _currentUserId = user.Identity.Name;
        }
    }

    public async Task ShowDialog()
    {
        NewFormat = new SyndicationFormatDto { IsActive = true };
        if (FormatDialog != null)
        {
            await FormatDialog.ShowAsync();
        }
    }

    private async Task SaveFormat()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(NewFormat.FormatName))
            {
                return;
            }

            var createdFormat = await SyndicationFormatService.CreateSyndicationFormatAsync(NewFormat, _currentUserId);

            NewFormat.SyndicationFormatId = createdFormat.SyndicationFormatId;

            await OnFormatCreated.InvokeAsync(NewFormat);
            await CloseDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating syndication format: {ex.Message}");
        }
    }

    private async Task CloseDialog()
    {
        if (FormatDialog != null)
        {
            await FormatDialog.HideAsync();
        }
        NewFormat = new();
    }
}
