﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet" href="@Assets["app.css"]" />
    <link rel="stylesheet" href="@Assets["GELProgramsInventory.styles.css"]" />
    <link href="_content/Syncfusion.Blazor.Themes/fluent2.css" rel="stylesheet" />
    <link href="/bootstrap/bootstrap-grid.min.css" rel="stylesheet" />
    <link href="/css/rtl-support.css" rel="stylesheet" />
    <ImportMap />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <HeadOutlet @rendermode="InteractiveServer" />
</head>

<body>
    <CascadingAuthenticationState>
        <Routes @rendermode="InteractiveServer" />
    </CascadingAuthenticationState>
    <script src="_framework/blazor.web.js"></script>
    <script src="_content/Syncfusion.Blazor/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script src="js/fileDownload.js"></script>

</body>

</html>
