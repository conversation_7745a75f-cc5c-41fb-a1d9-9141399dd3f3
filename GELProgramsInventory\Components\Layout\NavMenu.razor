﻿@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.FluentUI.AspNetCore.Components.Icons
@inject GELProgramsInventory.Services.MenuService MenuService
@inject GELProgramsInventory.Services.UserService UserService
@inject AuthenticationStateProvider AuthenticationStateProvider
@rendermode InteractiveServer

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>

            <!-- Dynamic Menu Items without Module (Root Level) -->
            @foreach (var menu in _rootMenus)
            {
                <FluentNavLink Href="@menu.MenuLink" Icon="@GetMenuIcon(menu)" IconColor="Color.Accent">@menu.MemuTitle</FluentNavLink>
            }

            <!-- Reports Module -->
            @if (_reportsMenus.Any())
            {
                <FluentNavGroup Title="Reports" Icon="@(new Icons.Regular.Size20.DocumentTable())" IconColor="Color.Accent">
                    @foreach (var menu in _reportsMenus)
                    {
                        var trg=$"oo{menu.MenuId}";
                        <FluentNavLink Href="@menu.MenuLink" Icon="@GetMenuIcon(menu)" IconColor="Color.Accent" Target="@trg">@menu.MemuTitle</FluentNavLink>
                    }
                </FluentNavGroup>
            }

            <!-- Admin Module (Only for Admin Users) -->
            @if (_adminMenus.Any())
            {
                <FluentNavGroup Title="Admin" Icon="@(new Icons.Regular.Size20.Settings())" IconColor="Color.Accent">
                    @foreach (var menu in _adminMenus)
                    {
                        <FluentNavLink Href="@menu.MenuLink" Icon="@GetMenuIcon(menu)" IconColor="Color.Accent">@menu.MemuTitle</FluentNavLink>
                    }
                </FluentNavGroup>
            }

            <!-- Other Dynamic Modules -->
            @foreach (var moduleGroup in _otherModuleMenus)
            {
                <FluentNavGroup Title="@moduleGroup.Key" Icon="@GetModuleIcon(moduleGroup.Key)" IconColor="Color.Accent">
                    @foreach (var menu in moduleGroup.Value)
                    {
                        <FluentNavLink Href="@menu.MenuLink" Icon="@GetMenuIcon(menu)" IconColor="Color.Accent">@menu.MemuTitle</FluentNavLink>
                    }
                </FluentNavGroup>
            }
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
    private List<MenuDto> _rootMenus = new();
    private List<MenuDto> _reportsMenus = new();
    private List<MenuDto> _adminMenus = new();
    private Dictionary<string, List<MenuDto>> _otherModuleMenus = new();
    private string? _currentUserId;
    

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity is { IsAuthenticated: true })
        {
            _currentUserId = user.Identity.Name;
            await LoadUserMenus();
        }
        else
        {
            // Handle unauthenticated user, e.g., clear menus or show a login prompt
            _rootMenus.Clear();
            _reportsMenus.Clear();
            _adminMenus.Clear();
            _otherModuleMenus.Clear();
        }
    }

    private async Task LoadUserMenus()
    {
        try
        {
            // Get user's accessible menus based on User -> UserRole -> RoleMenu -> Menu chain
            var userMenus = await UserService.GetUserMenusAsync(_currentUserId);

            // Group menus by module
            _rootMenus = userMenus.Where(m => string.IsNullOrEmpty(m.Module)).ToList();
            _reportsMenus = userMenus.Where(m => m.Module == "Reports").ToList();
            _adminMenus = userMenus.Where(m => m.Module == "Admin").ToList();

            // Group other modules
            _otherModuleMenus = userMenus
                .Where(m => !string.IsNullOrEmpty(m.Module) && m.Module != "Reports" && m.Module != "Admin")
                .GroupBy(m => m.Module!)
                .ToDictionary(g => g.Key, g => g.ToList());
        }
        catch (Exception ex)
        {
            // Log error and show fallback menus if needed
            Console.WriteLine($"Error loading user menus: {ex.Message}");
            // Could implement fallback logic here if needed
        }
    }

    /// <summary>
    /// Smart icon strategy based on menu title and link patterns
    /// </summary>
    private Icon GetMenuIcon(MenuDto menu)
    {
        // If menu has a custom icon, try to use it (future enhancement)
        if (!string.IsNullOrEmpty(menu.Icon))
        {
            return ParseCustomIcon(menu.Icon);
        }

        // Smart icon detection based on menu title and link
        var title = menu.MemuTitle?.ToLower() ?? "";
        var link = menu.MenuLink?.ToLower() ?? "";

        return (title, link) switch
        {
            // Program related
            var (t, l) when t.Contains("program") || l.Contains("program") => new Icons.Regular.Size20.Tv(),
            var (t, l) when t.Contains("syndication") || l.Contains("syndication") => new Icons.Regular.Size20.Status(),

            // People related
            var (t, l) when t.Contains("client") || l.Contains("client") => new Icons.Regular.Size20.People(),
            var (t, l) when t.Contains("user") || l.Contains("user") => new Icons.Regular.Size20.Person(),
            var (t, l) when t.Contains("role") || l.Contains("role") => new Icons.Regular.Size20.PersonAccounts(),
            var (t, l) when t.Contains("employee") || l.Contains("employee") => new Icons.Regular.Size20.PersonCircle(),

            // Business related
            var (t, l) when t.Contains("production") || t.Contains("house") || l.Contains("production") => new Icons.Regular.Size20.Building(),
            var (t, l) when t.Contains("company") || t.Contains("organization") => new Icons.Regular.Size20.BuildingBank(),

            // Reports and documents
            var (t, l) when t.Contains("report") || l.Contains("report") => new Icons.Regular.Size20.DocumentTable(),
            var (t, l) when t.Contains("archive") || l.Contains("archive") => new Icons.Regular.Size20.Archive(),
            var (t, l) when t.Contains("document") || l.Contains("document") => new Icons.Regular.Size20.Document(),
            var (t, l) when t.Contains("contract") || l.Contains("contract") => new Icons.Regular.Size20.DocumentText(),
            var (t, l) when t.Contains("invoice") || l.Contains("invoice") => new Icons.Regular.Size20.DocumentBulletList(),

            // Admin and settings
            var (t, l) when t.Contains("menu") || l.Contains("menu") => new Icons.Regular.Size20.Navigation(),
            var (t, l) when t.Contains("setting") || l.Contains("setting") => new Icons.Regular.Size20.Settings(),
            var (t, l) when t.Contains("config") || l.Contains("config") => new Icons.Regular.Size20.Wrench(),
            var (t, l) when t.Contains("admin") || l.Contains("admin") => new Icons.Regular.Size20.ShieldKeyhole(),

            // Financial
            var (t, l) when t.Contains("payment") || l.Contains("payment") => new Icons.Regular.Size20.Payment(),
            var (t, l) when t.Contains("currency") || l.Contains("currency") => new Icons.Regular.Size20.CurrencyDollarRupee(),
            var (t, l) when t.Contains("territory") || l.Contains("territory") => new Icons.Regular.Size20.Globe(),

            // Media and content
            var (t, l) when t.Contains("genre") || l.Contains("genre") => new Icons.Regular.Size20.MusicNote2(),
            var (t, l) when t.Contains("format") || l.Contains("format") => new Icons.Regular.Size20.DocumentSettings(),
            var (t, l) when t.Contains("language") || l.Contains("language") => new Icons.Regular.Size20.LocalLanguage(),

            // Dashboard and analytics
            var (t, l) when t.Contains("dashboard") || l.Contains("dashboard") => new Icons.Regular.Size20.ChartMultiple(),
            var (t, l) when t.Contains("analytics") || l.Contains("analytics") => new Icons.Regular.Size20.DataBarVertical(),
            var (t, l) when t.Contains("status") || l.Contains("status") => new Icons.Regular.Size20.Status(),

            // Default fallback
            _ => new Icons.Regular.Size20.Circle()
        };
    }

    /// <summary>
    /// Get appropriate icon for module groups
    /// </summary>
    private Icon GetModuleIcon(string moduleName)
    {
        return moduleName.ToLower() switch
        {
            "reports" => new Icons.Regular.Size20.DocumentTable(),
            "admin" => new Icons.Regular.Size20.Settings(),
            "finance" => new Icons.Regular.Size20.Payment(),
            "content" => new Icons.Regular.Size20.VideoClip(),
            "management" => new Icons.Regular.Size20.Organization(),
            "analytics" => new Icons.Regular.Size20.ChartMultiple(),
            "security" => new Icons.Regular.Size20.ShieldKeyhole(),
            _ => new Icons.Regular.Size20.Folder()
        };
    }

    /// <summary>
    /// Parse custom icon strings (for future enhancement when icons are stored in database)
    /// </summary>
    private Icon ParseCustomIcon(string iconString)
    {
        // This can be enhanced to parse various icon formats
        // For now, return a default icon
        return new Icons.Regular.Size20.Circle();
    }
}
