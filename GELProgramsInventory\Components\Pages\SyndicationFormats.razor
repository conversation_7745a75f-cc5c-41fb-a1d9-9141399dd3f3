@page "/syndication-formats"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Components.Shared
@using Microsoft.AspNetCore.Authorization
@inject SyndicationFormatService SyndicationFormatService
@rendermode InteractiveServer
@attribute [Authorize]
@inject AuthenticationStateProvider AuthenticationStateProvider

<Breadcrumb CurrentPage="Syndication Formats" />

<h1>Syndication Formats</h1>

<SfButton OnClick="@OpenCreateDialog">Create New Syndication Format</SfButton>
<SfDialog @ref="dlgSyndicationFormat" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="600px" Visible="false" Height="400px">
    <DialogTemplates>
        <Header>Syndication Format Information</Header>
        <Content>
            <EditForm Model="_selectedSyndicationFormat" OnValidSubmit="SaveSyndicationFormat" FormName="SyndicationFormatForm">
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Syndication Format Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedSyndicationFormat.SyndicationFormatName"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md" style="display: flex; align-items: center; gap: 5px;" >
                        <span><b>Status</b></span> <SfSwitch @bind-Checked="_selectedSyndicationFormat.IsActive"></SfSwitch> <span> @_selectedSyndicationFormat.Status</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfButton CssClass="e-primary">Save</SfButton>
                        
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfGrid @ref="_grid" DataSource="@_syndicationFormatsList" AllowPaging="true">
    <GridColumns>
        
        <GridColumn Field="@nameof(SyndicationFormatDto.SyndicationFormatName)" HeaderText="Syndication Format Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(SyndicationFormatDto.Status)" HeaderText="Status" AutoFit="true" ></GridColumn>
        
        <GridColumn HeaderText="Actions" Width="150">
            <Template Context="syndicationFormatContext">
                <SfButton OnClick="@(() => OpenEditDialog((SyndicationFormatDto)syndicationFormatContext))">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteSyndicationFormat(((SyndicationFormatDto)syndicationFormatContext).SyndicationFormatId))">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private List<SyndicationFormatDto> _syndicationFormatsList = new();
    private SfGrid<SyndicationFormatDto> _grid = new();
    private SyndicationFormatDto _selectedSyndicationFormat = new();
    private SfDialog? dlgSyndicationFormat;
    private string _currentUserId = "";
    
    protected override async Task OnInitializedAsync()
    {

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity is { IsAuthenticated: true })
        {
            _currentUserId = user.Identity.Name;
        }


        _syndicationFormatsList = await SyndicationFormatService.GetSyndicationFormatDtosAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedSyndicationFormat = new SyndicationFormatDto() {IsActive = true};
        await dlgSyndicationFormat!.ShowAsync();
    }

    private async Task OpenEditDialog(SyndicationFormatDto syndicationFormat)
    {
        _selectedSyndicationFormat = syndicationFormat;
        await dlgSyndicationFormat!.ShowAsync();
    }

    private async Task SaveSyndicationFormat()
    {
        if (_selectedSyndicationFormat.SyndicationFormatId == 0)
        {
            await SyndicationFormatService.CreateSyndicationFormatAsync(_selectedSyndicationFormat, _currentUserId);
        }
        else
        {
            await SyndicationFormatService.UpdateSyndicationFormatAsync(_selectedSyndicationFormat, _currentUserId);
        }

        _syndicationFormatsList = await SyndicationFormatService.GetSyndicationFormatDtosAsync();
        await _grid.Refresh();
        await dlgSyndicationFormat!.HideAsync();
    }

    private async Task DeleteSyndicationFormat(int syndicationFormatId)
    {
        await SyndicationFormatService.DeleteSyndicationFormatAsync(syndicationFormatId);
        _syndicationFormatsList = await SyndicationFormatService.GetSyndicationFormatDtosAsync();
        await _grid.Refresh();
    }

}
