using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;

namespace GELProgramsInventory.Services;

public class SyndicationFormatService(ApplicationDbContext context)
{
    public Task<List<SyndicationFormat>> GetSyndicationFormatsAsync()
    {
        var res = context.SyndicationFormats.ToList();
        return Task.FromResult(res);
    }

    public Task<List<SyndicationFormatDto>> GetSyndicationFormatDtosAsync()
    {
        var res = context.SyndicationFormats
            .Select(sf => new SyndicationFormatDto
            {
                SyndicationFormatId = sf.SyndicationFormatId,
                FormatName = sf.FormatName,
                SyndicationFormatName = sf.FormatName,
                IsActive = sf.IsActive
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<SyndicationFormatDto?> GetSyndicationFormatByIdAsync(int id)
    {
        var syndicationFormat = context.SyndicationFormats.Find(id);
        if (syndicationFormat == null) return Task.FromResult<SyndicationFormatDto?>(null);

        var res = new SyndicationFormatDto
        {
            SyndicationFormatId = syndicationFormat.SyndicationFormatId,
            FormatName = syndicationFormat.FormatName,
            SyndicationFormatName = syndicationFormat.FormatName,
            IsActive = syndicationFormat.IsActive
        };
        return Task.FromResult<SyndicationFormatDto?>(res);
    }

    public Task<SyndicationFormat> CreateSyndicationFormatAsync(SyndicationFormatDto syndicationFormatDto, string userId)
    {
        var syndicationFormat = new SyndicationFormat
        {
            FormatName = syndicationFormatDto.FormatName ?? syndicationFormatDto.SyndicationFormatName,
            IsActive = syndicationFormatDto.IsActive ?? true,
            CreatedDate = DateTime.Now
        };

        context.SyndicationFormats.Add(syndicationFormat);
        context.SaveChanges();
        return Task.FromResult(syndicationFormat);
    }

    public Task UpdateSyndicationFormatAsync(SyndicationFormatDto syndicationFormatDto, string userId)
    {
        var syndicationFormat = context.SyndicationFormats.Find(syndicationFormatDto.SyndicationFormatId);
        if (syndicationFormat != null)
        {
            syndicationFormat.FormatName =
                syndicationFormatDto.FormatName ?? syndicationFormatDto.SyndicationFormatName;
            syndicationFormat.IsActive = syndicationFormatDto.IsActive;
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    public Task DeleteSyndicationFormatAsync(int id)
    {
        var syndicationFormat = context.SyndicationFormats.Find(id);
        if (syndicationFormat != null)
        {
            context.SyndicationFormats.Remove(syndicationFormat);
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }
}