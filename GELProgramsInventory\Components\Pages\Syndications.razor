@page "/syndications"
@using GELProgramsInventory.Components.Shared
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Microsoft.AspNetCore.Authorization
@using Syncfusion.Blazor.Grids
@inject SyndicationService SyndicationService
@inject ContractDocumentService ContractDocumentService
@inject NavigationManager Navigation
@rendermode InteractiveServer
@attribute [Authorize]
@inject AuthenticationStateProvider AuthenticationStateProvider

<Breadcrumb CurrentPage="Syndications" />

<div class="">
    <div style="display: flex; justify-content: space-between; align-items: flex-end;" class="mb-2 me-2">
        <div class="">
            <h1 class="mb-0">Syndications Management</h1>
            <p class="page-description">Manage your program syndications and licensing agreements</p>
        </div>

        <div class="">
            <SfButton OnClick="NavigateToAddSyndication" CssClass="e-primary e-large" aria-label="Add new syndication">
                <span class="e-btn-icon e-icons e-plus"></span>
                Add New Syndication
            </SfButton>
        </div>
    </div>
    <div class="grid-container">
        <SfGrid @ref="_gridRef"
                DataSource="@_syndications"
                AllowPaging="true"
                AllowSorting="true"
                AllowFiltering="true"
                PageSettings="@(new GridPageSettings { PageSize = 10 })"
                Height="600">
            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Menu"></GridFilterSettings>


            <GridColumns>
                <GridColumn Field="ProgramName" HeaderText="Program Name" AutoFit="true"></GridColumn>
                <GridColumn Field="ClientName" HeaderText="Client" AutoFit="true"></GridColumn>
                <GridColumn Field="OnAirFormatName" HeaderText="On Air Format" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Episode" AutoFit="true" TextAlign="TextAlign.Right">
                    <Template Context="cc">
                        @{
                            if (cc is SyndicationDto mm)
                            {
                                <span>@mm.EpisodeFrom - @mm.EpisodeTo</span>

                            }
                        }
                    </Template>
                </GridColumn>
                <GridColumn Field="TotalEpisodes" HeaderText="Total Episodes" AutoFit="true" TextAlign="TextAlign.Right">
                    
                </GridColumn>
                <GridColumn Field="ContractStartDate" HeaderText="Contract Start" AutoFit="true" Format="d" Type="ColumnType.Date"></GridColumn>
                <GridColumn Field="ContractEndDate" HeaderText="Contract End" AutoFit="true" Format="d" Type="ColumnType.Date"></GridColumn>
                <GridColumn HeaderText="Documents" AutoFit="true" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            if (context is SyndicationDto syndication)
                            {
                                var docCount = GetDocumentCount(syndication.Id);
                                <div class="document-count">
                                    @if (docCount > 0)
                                    {
                                        <span class="badge badge-success">@docCount</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-secondary">0</span>
                                    }
                                </div>
                            }
                        }
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Actions" AutoFit="true">
                    <Template>
                        @{
                            if (context is SyndicationDto syndication)
                            {
                                <div class="action-buttons">
                                    <SfButton CssClass="e-small e-info" OnClick="@(() => EditSyndication(syndication.Id))" aria-label="Edit syndication">
                                        Edit
                                    </SfButton>

                                    <SfButton CssClass="e-small e-danger" OnClick="@(() => DeleteSyndication(syndication.Id))" aria-label="Delete syndication">
                                        Delete
                                    </SfButton>
                                </div>
                            }
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>

    <!-- Confirmation Dialog -->
    <SfDialog @ref="_confirmDialog" ShowCloseIcon="true" IsModal="true" Width="400px" Visible="false">
        <DialogTemplates>
            <Header>Confirm Delete</Header>
            <Content>
                <p>@_confirmMessage</p>
            </Content>
            <FooterTemplate>
                <SfButton OnClick="ConfirmDelete" CssClass="e-primary">Yes, Delete</SfButton>
                <SfButton OnClick="CancelDelete" CssClass="e-secondary">Cancel</SfButton>
            </FooterTemplate>
        </DialogTemplates>
    </SfDialog>
</div>

<style>
    .syndications-container {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-header {
        margin-bottom: 30px;
    }

        .page-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

    .page-description {
        color: #666;
        margin: 0;
    }

    .toolbar-section {
        margin-bottom: 20px;
        display: flex;
        justify-content: flex-start;
        gap: 15px;
    }

    .grid-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .action-buttons {
        display: flex;
        gap: 3px;
        flex-wrap: wrap;
    }

        .action-buttons .e-btn {
            min-width: auto;
            padding: 4px 6px;
            font-size: 0.75rem;
        }

    .document-count {
        display: flex;
        justify-content: center;
    }

    .badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.375rem;
    }

    .badge-success {
        color: #fff;
        background-color: #198754;
    }

    .badge-secondary {
        color: #fff;
        background-color: #6c757d;
    }

    @@media (max-width: 768px) {
        .syndications-container {
            padding: 15px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 3px;
        }

        .toolbar-section {
            flex-direction: column;
            align-items: flex-start;
        }
    }
</style>

@code {
    private SfGrid<SyndicationDto>? _gridRef;
    private SfDialog? _confirmDialog;
    private List<SyndicationDto> _syndications = new();
    private string _confirmMessage = "";
    private int _itemToDelete;
    private Dictionary<int, int> _documentCounts = new();
    private string _currentUserId = "";
    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity is { IsAuthenticated: true })
        {
            _currentUserId = user.Identity.Name;
        }

        await LoadSyndications();
    }

    private async Task LoadSyndications()
    {
        _syndications = await SyndicationService.GetSyndicationsAsync();
        await LoadDocumentCounts();
    }

    private async Task LoadDocumentCounts()
    {
        _documentCounts.Clear();
        foreach (var syndication in _syndications)
        {
            var count = await ContractDocumentService.GetContractDocumentCountBySyndicationIdAsync(syndication.Id);
            _documentCounts[syndication.Id] = count;
        }
    }

    private int CalculateTotalEpisodes(SyndicationDto syndication)
    {
        if (syndication.EpisodeFrom.HasValue && syndication.EpisodeTo.HasValue)
        {
            return (syndication.EpisodeTo.Value - syndication.EpisodeFrom.Value) + 1;
        }
        return 0;
    }

    private void NavigateToAddSyndication()
    {
        Navigation.NavigateTo("/syndications/add");
    }

    private void EditSyndication(int syndicationId)
    {
        Navigation.NavigateTo($"/syndications/edit/{syndicationId}");
    }

    private async Task DeleteSyndication(int syndicationId)
    {
        _itemToDelete = syndicationId;
        _confirmMessage = "Are you sure you want to delete this syndication? This action cannot be undone.";
        if (_confirmDialog != null)
        {
            await _confirmDialog.ShowAsync();
        }
    }

    private async Task ConfirmDelete()
    {
        try
        {
            await SyndicationService.SoftDeleteSyndicationAsync(_itemToDelete);
            await LoadSyndications();
            //if (_gridRef != null)
            //{
            //    await _gridRef.RefreshAsync();
            //}
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting syndication: {ex.Message}");
        }
        finally
        {
            await CancelDelete();
        }
    }

    private async Task CancelDelete()
    {
        if (_confirmDialog != null)
        {
            await _confirmDialog.HideAsync();
        }

        _itemToDelete = 0;
        _confirmMessage = "";
    }

    private int GetDocumentCount(int syndicationId)
    {
        return _documentCounts.TryGetValue(syndicationId, out var count) ? count : 0;
    }

    private void ViewDocuments(int syndicationId)
    {
        Navigation.NavigateTo($"/syndications/edit/{syndicationId}");
    }
}