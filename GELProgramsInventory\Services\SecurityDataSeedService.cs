using GELProgramsInventory.Models;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class SecurityDataSeedService(ApplicationDbContext context)
{
    public async Task SeedSecurityDataAsync()
    {
        // Check if data already exists
        if (await context.Users.AnyAsync() || await context.Roles.AnyAsync() || await context.Menus.AnyAsync())
        {
            return; // Data already seeded
        }

        // Create Roles
        var adminRole = new Role
        {
            RoleTitle = "Administrator",
            RoleIsActive = true,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = "system",
            ModifiedBy = "system"
        };

        var userRole = new Role
        {
            RoleTitle = "User",
            RoleIsActive = true,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = "system",
            ModifiedBy = "system"
        };

        var reportViewerRole = new Role
        {
            RoleTitle = "Report Viewer",
            RoleIsActive = true,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = "system",
            ModifiedBy = "system"
        };

        context.Roles.AddRange(adminRole, userRole, reportViewerRole);
        await context.SaveChangesAsync();

        // Create Menus
        var menus = new List<Menu>
        {
            // Root level menus
            new Menu
            {
                MemuTitle = "Programs",
                MenuLink = "/programs",
                SortOrder = 1,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = null,
                Icon = null
            },
            new Menu
            {
                MemuTitle = "Syndications",
                MenuLink = "/syndications",
                SortOrder = 2,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = null,
                Icon = null
            },
            new Menu
            {
                MemuTitle = "Clients",
                MenuLink = "/clients",
                SortOrder = 3,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = null,
                Icon = null
            },
            new Menu
            {
                MemuTitle = "Production Houses",
                MenuLink = "/production-houses",
                SortOrder = 4,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = null,
                Icon = null
            },

            // Reports module
            new Menu
            {
                MemuTitle = "Archive Report",
                MenuLink = "/reports/archive",
                SortOrder = 1,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = "Reports",
                Icon = null
            },
            new Menu
            {
                MemuTitle = "Program Status Report",
                MenuLink = "/reports/program-status",
                SortOrder = 2,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = "Reports",
                Icon = null
            },

            // Admin module
            new Menu
            {
                MemuTitle = "Users Management",
                MenuLink = "/admin/users",
                SortOrder = 1,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = "Admin",
                Icon = null
            },
            new Menu
            {
                MemuTitle = "Roles Management",
                MenuLink = "/admin/roles",
                SortOrder = 2,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = "Admin",
                Icon = null
            },
            new Menu
            {
                MemuTitle = "Menus Management",
                MenuLink = "/admin/menus",
                SortOrder = 3,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                CreatedBy = "system",
                ModifiedBy = "system",
                Module = "Admin",
                Icon = null
            }
        };

        context.Menus.AddRange(menus);
        await context.SaveChangesAsync();

        // Create Users
        var adminUser = new User
        {
            UserId = "khi-soft-056\\jawaid",
            FullName = "Jawaid Akhter",
            EmployeeCode = "EMP001",
            IsActive = true,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = "system",
            ModifiedBy = "system"
        };

        var testUser = new User
        {
            UserId = "test\\user",
            FullName = "Test User",
            EmployeeCode = "EMP002",
            IsActive = true,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = "system",
            ModifiedBy = "system"
        };

        context.Users.AddRange(adminUser, testUser);
        await context.SaveChangesAsync();

        // Assign roles to users
        var userRoles = new List<UserRole>
        {
            new UserRole
            {
                UserId = adminUser.UserId,
                RoleId = adminRole.RoleId,
                CreatedDate = DateTime.Now,
                CreatedBy = "system"
            },
            new UserRole
            {
                UserId = testUser.UserId,
                RoleId = userRole.RoleId,
                CreatedDate = DateTime.Now,
                CreatedBy = "system"
            },
            new UserRole
            {
                UserId = testUser.UserId,
                RoleId = reportViewerRole.RoleId,
                CreatedDate = DateTime.Now,
                CreatedBy = "system"
            }
        };

        context.UserRoles.AddRange(userRoles);
        await context.SaveChangesAsync();

        // Assign menus to roles
        var roleMenus = new List<RoleMenu>();

        // Admin role gets all menus
        foreach (var menu in menus)
        {
            roleMenus.Add(new RoleMenu
            {
                RoleId = adminRole.RoleId,
                MenuId = menu.MenuId,
                CreatedDate = DateTime.Now,
                CreatedBy = "system"
            });
        }

        // User role gets basic menus (no admin)
        var userMenus = menus.Where(m => m.Module != "Admin").ToList();
        foreach (var menu in userMenus)
        {
            roleMenus.Add(new RoleMenu
            {
                RoleId = userRole.RoleId,
                MenuId = menu.MenuId,
                CreatedDate = DateTime.Now,
                CreatedBy = "system"
            });
        }

        // Report Viewer role gets only reports
        var reportMenus = menus.Where(m => m.Module == "Reports").ToList();
        foreach (var menu in reportMenus)
        {
            roleMenus.Add(new RoleMenu
            {
                RoleId = reportViewerRole.RoleId,
                MenuId = menu.MenuId,
                CreatedDate = DateTime.Now,
                CreatedBy = "system"
            });
        }

        context.RoleMenus.AddRange(roleMenus);
        await context.SaveChangesAsync();
    }
}
